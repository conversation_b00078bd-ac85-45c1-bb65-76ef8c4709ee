# DeepDoc分离迁移指南

本指南详细说明如何从RAGFlow中分离DeepDoc模块，并将其打包为独立的工具包。

## 📋 分离步骤

### 1. 依赖分析和清理

#### 1.1 识别核心依赖
从RAGFlow的`pyproject.toml`中提取DeepDoc相关的依赖：

**核心依赖**:
```toml
# 图像处理
opencv-python>=4.8.0
pillow>=10.4.0
numpy>=1.26.0

# 机器学习推理
onnxruntime>=1.15.0  # CPU版本
onnxruntime-gpu>=1.15.0  # GPU版本

# 文档处理
pdfplumber>=0.10.0
pypdf>=5.0.0
openpyxl>=3.1.0
python-docx>=1.1.2
python-pptx>=1.0.2

# 模型下载
huggingface-hub>=0.19.0

# 并发处理
trio>=0.22.0

# 机器学习
scikit-learn>=1.5.0
xgboost
```

#### 1.2 移除RAGFlow特定依赖
需要移除或替换的依赖：
- `api.settings` → 自定义配置管理
- `api.utils.file_utils` → 标准库路径处理
- `rag.nlp` → 自定义NLP工具
- `rag.settings` → 自定义设置
- `rag.app.picture` → 自定义图像处理

### 2. 代码结构重组

#### 2.1 创建新的目录结构
```
standalone_deepdoc/
├── setup.py                    # 包安装配置
├── README.md                   # 项目说明
├── requirements.txt            # 依赖列表
├── LICENSE                     # 许可证
├── deepdoc/                    # 主包
│   ├── __init__.py            # 包初始化
│   ├── core.py                # 核心接口
│   ├── config.py              # 配置管理
│   ├── utils.py               # 工具函数
│   ├── cli.py                 # 命令行工具
│   ├── vision/                # 视觉模块
│   │   ├── __init__.py
│   │   ├── ocr.py
│   │   ├── layout_recognizer.py
│   │   ├── table_structure_recognizer.py
│   │   ├── recognizer.py
│   │   ├── operators.py
│   │   └── postprocess.py
│   └── parser/                # 解析器模块
│       ├── __init__.py
│       ├── base_parser.py
│       ├── pdf_parser.py
│       ├── docx_parser.py
│       ├── excel_parser.py
│       ├── ppt_parser.py
│       ├── html_parser.py
│       ├── json_parser.py
│       ├── markdown_parser.py
│       └── txt_parser.py
├── tests/                     # 测试代码
├── examples/                  # 示例代码
└── docs/                      # 文档
```

#### 2.2 代码迁移清单

**Vision模块迁移**:
- [x] `deepdoc/vision/__init__.py` - 统一接口
- [ ] `deepdoc/vision/ocr.py` - OCR实现（需要移除RAGFlow依赖）
- [ ] `deepdoc/vision/layout_recognizer.py` - 布局识别
- [ ] `deepdoc/vision/table_structure_recognizer.py` - 表格识别
- [ ] `deepdoc/vision/recognizer.py` - 基础识别器
- [ ] `deepdoc/vision/operators.py` - 图像操作
- [ ] `deepdoc/vision/postprocess.py` - 后处理

**Parser模块迁移**:
- [x] `deepdoc/parser/__init__.py` - 统一接口
- [ ] `deepdoc/parser/pdf_parser.py` - PDF解析器（需要重构）
- [ ] `deepdoc/parser/docx_parser.py` - DOCX解析器
- [ ] `deepdoc/parser/excel_parser.py` - Excel解析器
- [ ] `deepdoc/parser/ppt_parser.py` - PPT解析器
- [ ] `deepdoc/parser/html_parser.py` - HTML解析器
- [ ] `deepdoc/parser/json_parser.py` - JSON解析器
- [ ] `deepdoc/parser/markdown_parser.py` - Markdown解析器
- [ ] `deepdoc/parser/txt_parser.py` - TXT解析器

### 3. 依赖替换策略

#### 3.1 配置管理替换
**原始代码**:
```python
from api import settings
from api.utils.file_utils import get_project_base_directory
```

**替换为**:
```python
import os
from pathlib import Path

def get_model_cache_dir():
    """获取模型缓存目录"""
    cache_dir = os.environ.get('DEEPDOC_MODEL_CACHE', 
                              os.path.expanduser('~/.deepdoc/models'))
    Path(cache_dir).mkdir(parents=True, exist_ok=True)
    return cache_dir
```

#### 3.2 NLP工具替换
**原始代码**:
```python
from rag.nlp import rag_tokenizer
```

**替换为**:
```python
import re
from typing import List

def simple_tokenizer(text: str) -> List[str]:
    """简单的分词器"""
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text.strip())
    # 按句子分割
    sentences = re.split(r'[.!?。！？]', text)
    return [s.strip() for s in sentences if s.strip()]
```

#### 3.3 图像处理替换
**原始代码**:
```python
from rag.app.picture import vision_llm_chunk
```

**替换为**:
```python
def process_image_chunk(image_data, **kwargs):
    """处理图像块"""
    # 实现自定义的图像处理逻辑
    # 或者提供接口让用户自定义
    pass
```

### 4. 模型管理

#### 4.1 模型下载和缓存
```python
import os
from pathlib import Path
from huggingface_hub import snapshot_download

class ModelManager:
    def __init__(self, cache_dir=None):
        self.cache_dir = cache_dir or self._get_default_cache_dir()
        
    def _get_default_cache_dir(self):
        """获取默认缓存目录"""
        return os.path.expanduser('~/.deepdoc/models')
    
    def download_models(self):
        """下载所需模型"""
        model_repo = "InfiniFlow/deepdoc"
        local_dir = os.path.join(self.cache_dir, "deepdoc")
        
        try:
            snapshot_download(
                repo_id=model_repo,
                local_dir=local_dir,
                local_dir_use_symlinks=False
            )
            return local_dir
        except Exception as e:
            raise RuntimeError(f"模型下载失败: {e}")
```

#### 4.2 模型路径配置
```python
class Config:
    def __init__(self):
        self.model_cache_dir = os.environ.get(
            'DEEPDOC_MODEL_CACHE', 
            os.path.expanduser('~/.deepdoc/models')
        )
        self.device = os.environ.get('DEEPDOC_DEVICE', 'cpu')
        self.parallel_devices = int(os.environ.get('DEEPDOC_PARALLEL_DEVICES', '0'))
```

### 5. 接口设计

#### 5.1 统一的文档处理接口
```python
class DocumentProcessor:
    def parse(self, file_path: str, parser_type: str = "auto", **kwargs):
        """统一的文档解析接口"""
        pass
    
    def batch_parse(self, file_paths: List[str], **kwargs):
        """批量文档解析"""
        pass
```

#### 5.2 统一的视觉分析接口
```python
class DocumentAnalyzer:
    def analyze_image(self, image_path: str, **kwargs):
        """图像分析接口"""
        pass
    
    def batch_analyze(self, image_paths: List[str], **kwargs):
        """批量图像分析"""
        pass
```

### 6. 测试和验证

#### 6.1 单元测试
```python
import unittest
from deepdoc import DocumentProcessor, DocumentAnalyzer

class TestDeepDoc(unittest.TestCase):
    def setUp(self):
        self.processor = DocumentProcessor()
        self.analyzer = DocumentAnalyzer()
    
    def test_pdf_parsing(self):
        """测试PDF解析"""
        pass
    
    def test_ocr_recognition(self):
        """测试OCR识别"""
        pass
```

#### 6.2 集成测试
```python
def test_integration_with_langchain():
    """测试与LangChain的集成"""
    pass

def test_integration_with_llamaindex():
    """测试与LlamaIndex的集成"""
    pass
```

### 7. 打包和发布

#### 7.1 PyPI发布准备
```bash
# 构建包
python setup.py sdist bdist_wheel

# 检查包
twine check dist/*

# 上传到测试PyPI
twine upload --repository testpypi dist/*

# 上传到正式PyPI
twine upload dist/*
```

#### 7.2 Docker镜像
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN pip install -e .

CMD ["deepdoc-parse", "--help"]
```

### 8. 文档和示例

#### 8.1 API文档
- 使用Sphinx生成API文档
- 提供详细的使用示例
- 包含集成指南

#### 8.2 示例代码
- 基础使用示例
- 与主流RAG框架的集成示例
- 性能优化示例

### 9. 迁移检查清单

- [ ] 移除所有RAGFlow特定依赖
- [ ] 实现独立的配置管理
- [ ] 创建统一的API接口
- [ ] 编写完整的测试用例
- [ ] 准备详细的文档
- [ ] 设置CI/CD流程
- [ ] 准备PyPI发布
- [ ] 创建Docker镜像
- [ ] 编写迁移指南
- [ ] 提供集成示例

### 10. 注意事项

1. **许可证兼容性**: 确保新包的许可证与原项目兼容
2. **版本管理**: 建立清晰的版本号规则
3. **向后兼容**: 尽量保持API的向后兼容性
4. **性能优化**: 移除不必要的依赖以提高性能
5. **错误处理**: 增强错误处理和日志记录
6. **文档完整性**: 确保文档覆盖所有功能
7. **社区支持**: 建立问题反馈和贡献机制

通过以上步骤，可以成功将DeepDoc从RAGFlow中分离出来，创建一个独立、可复用的文档理解工具包。
