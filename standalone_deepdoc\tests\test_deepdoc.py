#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc测试套件
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch
import sys

# 添加deepdoc到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    import deepdoc
    DEEPDOC_AVAILABLE = True
except ImportError:
    DEEPDOC_AVAILABLE = False


class TestDeepDocImports(unittest.TestCase):
    """测试DeepDoc导入"""
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_main_imports(self):
        """测试主要导入"""
        # 测试主模块导入
        self.assertTrue(hasattr(deepdoc, '__version__'))
        self.assertTrue(hasattr(deepdoc, 'create_document_processor'))
        self.assertTrue(hasattr(deepdoc, 'create_document_analyzer'))
        
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_vision_imports(self):
        """测试视觉模块导入"""
        from deepdoc.vision import check_vision_availability
        
        # 检查视觉模块可用性
        available = check_vision_availability()
        self.assertIsInstance(available, bool)
        
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_parser_imports(self):
        """测试解析器模块导入"""
        from deepdoc.parser import check_parser_availability, get_supported_formats
        
        # 检查解析器模块可用性
        available = check_parser_availability()
        self.assertIsInstance(available, bool)
        
        # 检查支持的格式
        formats = get_supported_formats()
        self.assertIsInstance(formats, list)


class TestDocumentProcessor(unittest.TestCase):
    """测试文档处理器"""
    
    def setUp(self):
        """设置测试"""
        if DEEPDOC_AVAILABLE:
            self.processor = deepdoc.create_document_processor()
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_processor_creation(self):
        """测试处理器创建"""
        self.assertIsNotNone(self.processor)
        
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_format_detection(self):
        """测试格式检测"""
        from deepdoc.parser import detect_format
        
        # 测试不同文件格式的检测
        test_cases = [
            ("test.pdf", "pdf"),
            ("test.docx", "docx"),
            ("test.doc", "docx"),
            ("test.xlsx", "excel"),
            ("test.xls", "excel"),
            ("test.pptx", "ppt"),
            ("test.html", "html"),
            ("test.json", "json"),
            ("test.md", "markdown"),
            ("test.txt", "txt"),
            ("test.unknown", "txt"),  # 默认为txt
        ]
        
        for filename, expected_format in test_cases:
            with self.subTest(filename=filename):
                detected_format = detect_format(filename)
                self.assertEqual(detected_format, expected_format)
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_txt_parsing(self):
        """测试TXT文件解析"""
        # 创建临时TXT文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            test_content = "这是测试内容。\n这是第二行。\n这是第三行。"
            f.write(test_content)
            temp_file = f.name
        
        try:
            # 解析文件
            results = self.processor.parse(temp_file, "txt")
            
            # 验证结果
            self.assertIsInstance(results, list)
            self.assertGreater(len(results), 0)
            
            # 验证内容
            all_text = " ".join([chunk.get('text', '') for chunk in results])
            self.assertIn("测试内容", all_text)
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)


class TestDocumentAnalyzer(unittest.TestCase):
    """测试文档分析器"""
    
    def setUp(self):
        """设置测试"""
        if DEEPDOC_AVAILABLE:
            try:
                self.analyzer = deepdoc.create_document_analyzer()
            except Exception:
                self.analyzer = None
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_analyzer_creation(self):
        """测试分析器创建"""
        # 注意：如果视觉模块依赖不可用，analyzer可能为None
        if self.analyzer is not None:
            self.assertIsNotNone(self.analyzer)


class TestConfig(unittest.TestCase):
    """测试配置管理"""
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_config_access(self):
        """测试配置访问"""
        config = deepdoc.config
        
        # 测试配置属性
        self.assertIsNotNone(config.model_cache_dir)
        self.assertIsNotNone(config.device)
        self.assertIsInstance(config.batch_size, int)
        
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_config_modification(self):
        """测试配置修改"""
        config = deepdoc.config
        
        # 保存原始值
        original_device = config.device
        
        try:
            # 修改配置
            config.set_device("cuda")
            self.assertEqual(config.device, "cuda")
            
        finally:
            # 恢复原始值
            config.set_device(original_device)


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_quick_functions(self):
        """测试快速函数"""
        # 测试快速解析函数存在
        self.assertTrue(hasattr(deepdoc, 'quick_parse'))
        self.assertTrue(hasattr(deepdoc, 'quick_ocr'))
        self.assertTrue(hasattr(deepdoc, 'quick_layout_analysis'))
        
        # 测试函数是可调用的
        self.assertTrue(callable(deepdoc.quick_parse))
        self.assertTrue(callable(deepdoc.quick_ocr))
        self.assertTrue(callable(deepdoc.quick_layout_analysis))


class TestCLI(unittest.TestCase):
    """测试命令行工具"""
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_cli_imports(self):
        """测试CLI模块导入"""
        try:
            from deepdoc import cli
            self.assertTrue(hasattr(cli, 'ocr_cli'))
            self.assertTrue(hasattr(cli, 'layout_cli'))
            self.assertTrue(hasattr(cli, 'table_cli'))
            self.assertTrue(hasattr(cli, 'parse_cli'))
        except ImportError:
            self.skipTest("CLI模块不可用")


class TestIntegration(unittest.TestCase):
    """测试集成功能"""
    
    @unittest.skipUnless(DEEPDOC_AVAILABLE, "DeepDoc不可用")
    def test_end_to_end_txt_processing(self):
        """测试端到端TXT处理"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            test_content = """这是一个测试文档。
            
第一段内容包含一些重要信息。

第二段内容包含更多详细信息。
这里有一些技术细节。

第三段是总结部分。"""
            f.write(test_content)
            temp_file = f.name
        
        try:
            # 使用快速解析函数
            results = deepdoc.quick_parse(temp_file)
            
            # 验证结果
            self.assertIsInstance(results, list)
            self.assertGreater(len(results), 0)
            
            # 验证每个结果都有必要的字段
            for chunk in results:
                self.assertIsInstance(chunk, dict)
                self.assertIn('text', chunk)
                
        finally:
            # 清理
            os.unlink(temp_file)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDeepDocImports,
        TestDocumentProcessor,
        TestDocumentAnalyzer,
        TestConfig,
        TestUtils,
        TestCLI,
        TestIntegration,
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    print("DeepDoc测试套件")
    print("=" * 50)
    
    if not DEEPDOC_AVAILABLE:
        print("❌ DeepDoc模块不可用，请先安装")
        sys.exit(1)
    
    print(f"✓ DeepDoc版本: {deepdoc.__version__}")
    print(f"✓ Vision模块可用: {deepdoc.vision.check_vision_availability()}")
    print(f"✓ Parser模块可用: {deepdoc.parser.check_parser_availability()}")
    print()
    
    # 运行测试
    success = run_tests()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
