#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc Core Components
提供高级文档处理和分析接口
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from PIL import Image
import numpy as np

from .vision import OCR, LayoutRecognizer, TableStructureRecognizer
from .parser import (
    PdfParser, DocxParser, ExcelParser, PptParser, 
    HtmlParser, JsonParser, MarkdownParser, TxtParser
)

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """
    文档处理器 - 提供统一的文档解析接口
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化文档处理器
        
        Args:
            config (dict, optional): 配置参数
        """
        self.config = config or {}
        self._parsers = {}
        self._init_parsers()
        
    def _init_parsers(self):
        """初始化解析器"""
        try:
            self._parsers = {
                'pdf': PdfParser(),
                'docx': DocxParser(),
                'excel': ExcelParser(),
                'ppt': PptParser(),
                'html': HtmlParser(),
                'json': JsonParser(),
                'markdown': MarkdownParser(),
                'txt': TxtParser(),
            }
            logger.info("文档解析器初始化完成")
        except Exception as e:
            logger.error(f"解析器初始化失败: {e}")
            raise
    
    def parse(self, file_path: Union[str, Path], parser_type: str = "auto", **kwargs) -> List[Dict[str, Any]]:
        """
        解析文档
        
        Args:
            file_path (str|Path): 文档路径
            parser_type (str): 解析器类型，支持 "auto", "pdf", "docx", "excel", "ppt", "html", "json", "markdown", "txt"
            **kwargs: 其他参数
            
        Returns:
            list: 解析结果
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 自动检测文件类型
        if parser_type == "auto":
            parser_type = self._detect_file_type(file_path)
            
        if parser_type not in self._parsers:
            raise ValueError(f"不支持的解析器类型: {parser_type}")
            
        parser = self._parsers[parser_type]
        
        try:
            logger.info(f"开始解析文档: {file_path} (类型: {parser_type})")
            result = parser.parse(str(file_path), **kwargs)
            logger.info(f"文档解析完成，共解析出 {len(result)} 个片段")
            return result
        except Exception as e:
            logger.error(f"文档解析失败: {e}")
            raise
    
    def _detect_file_type(self, file_path: Path) -> str:
        """
        检测文件类型
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 文件类型
        """
        suffix = file_path.suffix.lower()
        
        type_mapping = {
            '.pdf': 'pdf',
            '.docx': 'docx',
            '.doc': 'docx',
            '.xlsx': 'excel',
            '.xls': 'excel',
            '.pptx': 'ppt',
            '.ppt': 'ppt',
            '.html': 'html',
            '.htm': 'html',
            '.json': 'json',
            '.md': 'markdown',
            '.txt': 'txt',
        }
        
        return type_mapping.get(suffix, 'txt')
    
    def batch_parse(self, file_paths: List[Union[str, Path]], **kwargs) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量解析文档
        
        Args:
            file_paths (list): 文档路径列表
            **kwargs: 其他参数
            
        Returns:
            dict: 解析结果字典，键为文件路径，值为解析结果
        """
        results = {}
        
        for file_path in file_paths:
            try:
                result = self.parse(file_path, **kwargs)
                results[str(file_path)] = result
            except Exception as e:
                logger.error(f"解析文件失败 {file_path}: {e}")
                results[str(file_path)] = []
                
        return results


class DocumentAnalyzer:
    """
    文档分析器 - 提供视觉分析功能
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化文档分析器
        
        Args:
            config (dict, optional): 配置参数
        """
        self.config = config or {}
        self._init_vision_models()
        
    def _init_vision_models(self):
        """初始化视觉模型"""
        try:
            self.ocr = OCR()
            self.layout_recognizer = LayoutRecognizer()
            self.table_recognizer = TableStructureRecognizer()
            logger.info("视觉模型初始化完成")
        except Exception as e:
            logger.error(f"视觉模型初始化失败: {e}")
            raise
    
    def analyze_image(self, image_path: Union[str, Path], 
                     include_ocr: bool = True,
                     include_layout: bool = True,
                     include_table: bool = True,
                     **kwargs) -> Dict[str, Any]:
        """
        分析图像
        
        Args:
            image_path (str|Path): 图像路径
            include_ocr (bool): 是否包含OCR
            include_layout (bool): 是否包含布局分析
            include_table (bool): 是否包含表格分析
            **kwargs: 其他参数
            
        Returns:
            dict: 分析结果
        """
        image_path = Path(image_path)
        
        if not image_path.exists():
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
        # 加载图像
        image = Image.open(image_path)
        if image.mode != 'RGB':
            image = image.convert('RGB')
        image_array = np.array(image)
        
        results = {
            'image_path': str(image_path),
            'image_size': image.size,
        }
        
        try:
            # OCR识别
            if include_ocr:
                logger.info("开始OCR识别")
                ocr_results = self.ocr.recognize(image_array, **kwargs)
                results['ocr'] = ocr_results
                
            # 布局分析
            if include_layout:
                logger.info("开始布局分析")
                layout_results = self.layout_recognizer.analyze([image_array], **kwargs)
                results['layout'] = layout_results
                
            # 表格分析
            if include_table:
                logger.info("开始表格分析")
                table_results = self.table_recognizer.analyze([image_array], **kwargs)
                results['tables'] = table_results
                
            logger.info("图像分析完成")
            return results
            
        except Exception as e:
            logger.error(f"图像分析失败: {e}")
            raise
    
    def analyze_pdf_pages(self, pdf_path: Union[str, Path], 
                         page_range: Optional[tuple] = None,
                         **kwargs) -> List[Dict[str, Any]]:
        """
        分析PDF页面
        
        Args:
            pdf_path (str|Path): PDF路径
            page_range (tuple, optional): 页面范围 (start, end)
            **kwargs: 其他参数
            
        Returns:
            list: 每页的分析结果
        """
        # 这里需要实现PDF页面转图像的逻辑
        # 然后对每页进行分析
        pass
    
    def batch_analyze(self, image_paths: List[Union[str, Path]], **kwargs) -> List[Dict[str, Any]]:
        """
        批量分析图像
        
        Args:
            image_paths (list): 图像路径列表
            **kwargs: 其他参数
            
        Returns:
            list: 分析结果列表
        """
        results = []
        
        for image_path in image_paths:
            try:
                result = self.analyze_image(image_path, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"分析图像失败 {image_path}: {e}")
                results.append({
                    'image_path': str(image_path),
                    'error': str(e)
                })
                
        return results
