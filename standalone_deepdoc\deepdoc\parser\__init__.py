#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc Parser Module
文档解析模块，支持多种文档格式的解析
"""

import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

logger = logging.getLogger(__name__)

# 导入具体解析器实现
try:
    from .pdf_parser import PdfParserEngine
    from .docx_parser import DocxParserEngine
    from .excel_parser import ExcelParserEngine
    from .ppt_parser import PptParserEngine
    from .html_parser import HtmlParserEngine
    from .json_parser import JsonParserEngine
    from .markdown_parser import MarkdownParserEngine
    from .txt_parser import TxtParserEngine
    
    # 创建统一接口
    class BaseParser:
        """解析器基类"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            self.config = config or {}
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """
            解析文档
            
            Args:
                file_path: 文档路径
                **kwargs: 其他参数
                
            Returns:
                list: 解析结果
            """
            raise NotImplementedError
    
    class PdfParser(BaseParser):
        """PDF解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = PdfParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析PDF文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class DocxParser(BaseParser):
        """DOCX解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = DocxParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析DOCX文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class ExcelParser(BaseParser):
        """Excel解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = ExcelParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析Excel文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class PptParser(BaseParser):
        """PPT解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = PptParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析PPT文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class HtmlParser(BaseParser):
        """HTML解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = HtmlParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析HTML文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class JsonParser(BaseParser):
        """JSON解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = JsonParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析JSON文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class MarkdownParser(BaseParser):
        """Markdown解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = MarkdownParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析Markdown文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    class TxtParser(BaseParser):
        """TXT解析器"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            super().__init__(config)
            self.engine = TxtParserEngine(self.config)
            
        def parse(self, file_path: Union[str, Path], **kwargs) -> List[Dict[str, Any]]:
            """解析TXT文档"""
            return self.engine.parse(str(file_path), **kwargs)
    
    # 标记模块可用
    PARSER_AVAILABLE = True
    logger.info("DeepDoc Parser模块加载成功")
    
except ImportError as e:
    logger.warning(f"DeepDoc Parser模块加载失败: {e}")
    
    # 创建占位符类
    class BaseParser:
        def __init__(self, config=None):
            raise ImportError("Parser模块不可用，请检查依赖安装")
    
    PdfParser = BaseParser
    DocxParser = BaseParser
    ExcelParser = BaseParser
    PptParser = BaseParser
    HtmlParser = BaseParser
    JsonParser = BaseParser
    MarkdownParser = BaseParser
    TxtParser = BaseParser
    
    PARSER_AVAILABLE = False

# 导出接口
__all__ = [
    "BaseParser",
    "PdfParser",
    "DocxParser",
    "ExcelParser", 
    "PptParser",
    "HtmlParser",
    "JsonParser",
    "MarkdownParser",
    "TxtParser",
    "PARSER_AVAILABLE",
]

# 解析器注册表
PARSER_REGISTRY = {
    'pdf': PdfParser,
    'docx': DocxParser,
    'excel': ExcelParser,
    'ppt': PptParser,
    'html': HtmlParser,
    'json': JsonParser,
    'markdown': MarkdownParser,
    'txt': TxtParser,
}

# 便捷函数
def create_parser(parser_type: str, config: Optional[Dict[str, Any]] = None) -> BaseParser:
    """
    创建解析器实例
    
    Args:
        parser_type: 解析器类型
        config: 配置参数
        
    Returns:
        BaseParser: 解析器实例
    """
    if parser_type not in PARSER_REGISTRY:
        raise ValueError(f"不支持的解析器类型: {parser_type}")
    
    parser_class = PARSER_REGISTRY[parser_type]
    return parser_class(config)

def get_supported_formats() -> List[str]:
    """获取支持的文档格式"""
    return list(PARSER_REGISTRY.keys())

def detect_format(file_path: Union[str, Path]) -> str:
    """
    检测文档格式
    
    Args:
        file_path: 文档路径
        
    Returns:
        str: 文档格式
    """
    file_path = Path(file_path)
    suffix = file_path.suffix.lower()
    
    format_mapping = {
        '.pdf': 'pdf',
        '.docx': 'docx',
        '.doc': 'docx',
        '.xlsx': 'excel',
        '.xls': 'excel',
        '.pptx': 'ppt',
        '.ppt': 'ppt',
        '.html': 'html',
        '.htm': 'html',
        '.json': 'json',
        '.md': 'markdown',
        '.txt': 'txt',
    }
    
    return format_mapping.get(suffix, 'txt')

def quick_parse(file_path: Union[str, Path], parser_type: str = "auto", **kwargs) -> List[Dict[str, Any]]:
    """
    快速解析文档
    
    Args:
        file_path: 文档路径
        parser_type: 解析器类型，"auto"表示自动检测
        **kwargs: 其他参数
        
    Returns:
        list: 解析结果
    """
    if parser_type == "auto":
        parser_type = detect_format(file_path)
    
    parser = create_parser(parser_type)
    return parser.parse(file_path, **kwargs)

# 检查模块可用性
def check_parser_availability() -> bool:
    """检查解析器模块是否可用"""
    return PARSER_AVAILABLE

def get_parser_info() -> Dict[str, Any]:
    """获取解析器模块信息"""
    return {
        "available": PARSER_AVAILABLE,
        "supported_formats": get_supported_formats() if PARSER_AVAILABLE else [],
        "parsers": {
            format_name: PARSER_AVAILABLE 
            for format_name in get_supported_formats()
        } if PARSER_AVAILABLE else {}
    }
