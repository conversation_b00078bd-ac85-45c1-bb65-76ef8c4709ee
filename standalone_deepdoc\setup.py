#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone DeepDoc Package Setup
从RAGFlow中分离出来的独立文档理解工具包
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Standalone DeepDoc - Advanced Document Understanding Toolkit"

# 读取requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="standalone-deepdoc",
    version="1.0.0",
    description="Advanced Document Understanding Toolkit - Standalone version extracted from RAGFlow",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="DeepDoc Community",
    author_email="<EMAIL>",
    url="https://github.com/your-org/standalone-deepdoc",
    packages=find_packages(),
    include_package_data=True,
    package_data={
        'deepdoc': ['models/*', 'configs/*'],
    },
    install_requires=[
        # 核心依赖
        "numpy>=1.26.0,<2.0.0",
        "opencv-python>=4.8.0",
        "pillow>=10.4.0",
        "onnxruntime>=1.15.0",
        
        # PDF处理
        "pdfplumber>=0.10.0",
        "pypdf>=5.0.0,<6.0.0",
        
        # Excel处理
        "openpyxl>=3.1.0,<4.0.0",
        "pandas>=2.2.0,<3.0.0",
        
        # DOCX/PPT处理
        "python-docx>=1.1.2",
        "python-pptx>=1.0.2",
        
        # 机器学习
        "scikit-learn>=1.5.0",
        "xgboost",
        
        # 模型下载
        "huggingface-hub>=0.19.0",
        
        # 并发处理
        "trio>=0.22.0",
        
        # 其他工具
        "tqdm>=4.65.0",
        "requests>=2.32.0",
    ],
    extras_require={
        "gpu": [
            "onnxruntime-gpu>=1.15.0",
        ],
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "full": [
            "torch>=2.0.0",
            "transformers>=4.30.0",
        ]
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: Apache Software License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Text Processing :: Linguistic",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="document-understanding ocr layout-recognition table-extraction pdf-parser",
    entry_points={
        'console_scripts': [
            'deepdoc-ocr=deepdoc.cli:ocr_cli',
            'deepdoc-layout=deepdoc.cli:layout_cli',
            'deepdoc-table=deepdoc.cli:table_cli',
            'deepdoc-parse=deepdoc.cli:parse_cli',
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/your-org/standalone-deepdoc/issues",
        "Source": "https://github.com/your-org/standalone-deepdoc",
        "Documentation": "https://standalone-deepdoc.readthedocs.io/",
    },
)
