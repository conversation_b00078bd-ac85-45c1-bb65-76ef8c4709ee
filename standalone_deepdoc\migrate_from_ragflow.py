#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow DeepDoc迁移脚本
自动化从RAGFlow中分离DeepDoc模块的过程
"""

import os
import re
import shutil
import argparse
from pathlib import Path
from typing import List, Dict, Set


class DeepDocMigrator:
    """DeepDoc迁移器"""
    
    def __init__(self, ragflow_path: str, output_path: str):
        self.ragflow_path = Path(ragflow_path)
        self.output_path = Path(output_path)
        self.deepdoc_source = self.ragflow_path / "deepdoc"
        self.deepdoc_target = self.output_path / "deepdoc"
        
        # 需要替换的导入映射
        self.import_replacements = {
            "from api import settings": "from .config import settings",
            "from api.utils.file_utils import get_project_base_directory": "from .utils import get_model_cache_dir",
            "from rag.nlp import rag_tokenizer": "from .utils import simple_tokenizer as rag_tokenizer",
            "from rag.settings import PARALLEL_DEVICES": "from .config import PARALLEL_DEVICES",
            "from rag.app.picture import vision_llm_chunk": "from .utils import vision_llm_chunk",
            "from rag.prompts import vision_llm_describe_prompt": "from .utils import vision_llm_describe_prompt",
        }
        
        # 需要移除的导入
        self.imports_to_remove = {
            "from beartype.claw import beartype_this_package",
            "beartype_this_package()",
        }
    
    def migrate(self):
        """执行完整的迁移过程"""
        print("开始DeepDoc迁移过程...")
        
        # 1. 验证源路径
        self._validate_source()
        
        # 2. 创建目标目录结构
        self._create_target_structure()
        
        # 3. 复制和修改源文件
        self._copy_and_modify_files()
        
        # 4. 创建配置文件
        self._create_config_files()
        
        # 5. 创建工具文件
        self._create_utils_files()
        
        # 6. 生成requirements.txt
        self._generate_requirements()
        
        print(f"迁移完成！输出目录: {self.output_path}")
    
    def _validate_source(self):
        """验证源路径"""
        if not self.ragflow_path.exists():
            raise FileNotFoundError(f"RAGFlow路径不存在: {self.ragflow_path}")
        
        if not self.deepdoc_source.exists():
            raise FileNotFoundError(f"DeepDoc源目录不存在: {self.deepdoc_source}")
        
        print(f"✓ 验证源路径: {self.ragflow_path}")
    
    def _create_target_structure(self):
        """创建目标目录结构"""
        # 创建主目录
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        directories = [
            "deepdoc",
            "deepdoc/vision", 
            "deepdoc/parser",
            "tests",
            "examples",
            "docs",
        ]
        
        for dir_name in directories:
            (self.output_path / dir_name).mkdir(parents=True, exist_ok=True)
        
        print("✓ 创建目录结构")
    
    def _copy_and_modify_files(self):
        """复制和修改源文件"""
        # 复制vision模块
        self._copy_vision_module()
        
        # 复制parser模块
        self._copy_parser_module()
        
        # 复制主__init__.py
        self._copy_main_init()
        
        print("✓ 复制和修改源文件")
    
    def _copy_vision_module(self):
        """复制vision模块"""
        vision_source = self.deepdoc_source / "vision"
        vision_target = self.deepdoc_target / "vision"
        
        if not vision_source.exists():
            print("⚠️ Vision模块不存在，跳过")
            return
        
        # 复制所有Python文件
        for py_file in vision_source.glob("*.py"):
            target_file = vision_target / py_file.name
            content = self._modify_file_content(py_file.read_text(encoding='utf-8'))
            target_file.write_text(content, encoding='utf-8')
        
        print("  ✓ 复制vision模块")
    
    def _copy_parser_module(self):
        """复制parser模块"""
        parser_source = self.deepdoc_source / "parser"
        parser_target = self.deepdoc_target / "parser"
        
        if not parser_source.exists():
            print("⚠️ Parser模块不存在，跳过")
            return
        
        # 复制所有Python文件
        for py_file in parser_source.glob("*.py"):
            target_file = parser_target / py_file.name
            content = self._modify_file_content(py_file.read_text(encoding='utf-8'))
            target_file.write_text(content, encoding='utf-8')
        
        # 复制resume子目录（如果存在）
        resume_source = parser_source / "resume"
        if resume_source.exists():
            resume_target = parser_target / "resume"
            resume_target.mkdir(exist_ok=True)
            for py_file in resume_source.glob("*.py"):
                target_file = resume_target / py_file.name
                content = self._modify_file_content(py_file.read_text(encoding='utf-8'))
                target_file.write_text(content, encoding='utf-8')
        
        print("  ✓ 复制parser模块")
    
    def _copy_main_init(self):
        """复制主__init__.py"""
        init_source = self.deepdoc_source / "__init__.py"
        init_target = self.deepdoc_target / "__init__.py"
        
        if init_source.exists():
            content = self._modify_file_content(init_source.read_text(encoding='utf-8'))
            init_target.write_text(content, encoding='utf-8')
        
        print("  ✓ 复制主__init__.py")
    
    def _modify_file_content(self, content: str) -> str:
        """修改文件内容，替换导入和移除依赖"""
        # 替换导入
        for old_import, new_import in self.import_replacements.items():
            content = content.replace(old_import, new_import)
        
        # 移除特定导入
        for import_to_remove in self.imports_to_remove:
            content = content.replace(import_to_remove, "")
        
        # 移除多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        return content
    
    def _create_config_files(self):
        """创建配置文件"""
        config_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc配置管理
"""

import os
from pathlib import Path

# 默认配置
DEFAULT_MODEL_CACHE_DIR = os.path.expanduser('~/.deepdoc/models')
DEFAULT_DEVICE = 'cpu'
DEFAULT_PARALLEL_DEVICES = 0

class Settings:
    """设置类"""
    
    def __init__(self):
        self.model_cache_dir = os.environ.get('DEEPDOC_MODEL_CACHE', DEFAULT_MODEL_CACHE_DIR)
        self.device = os.environ.get('DEEPDOC_DEVICE', DEFAULT_DEVICE)
        self.parallel_devices = int(os.environ.get('DEEPDOC_PARALLEL_DEVICES', DEFAULT_PARALLEL_DEVICES))
        
        # 确保模型缓存目录存在
        Path(self.model_cache_dir).mkdir(parents=True, exist_ok=True)

# 全局设置实例
settings = Settings()

# 兼容性变量
PARALLEL_DEVICES = settings.parallel_devices
'''
        
        config_file = self.deepdoc_target / "config.py"
        config_file.write_text(config_content, encoding='utf-8')
        
        print("  ✓ 创建配置文件")
    
    def _create_utils_files(self):
        """创建工具文件"""
        utils_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc工具函数
"""

import os
import re
from pathlib import Path
from typing import List, Any

def get_model_cache_dir() -> str:
    """获取模型缓存目录"""
    from .config import settings
    return settings.model_cache_dir

def simple_tokenizer(text: str) -> List[str]:
    """简单的分词器，替代rag_tokenizer"""
    if not text:
        return []
    
    # 移除多余空白
    text = re.sub(r'\\s+', ' ', text.strip())
    
    # 按句子分割
    sentences = re.split(r'[.!?。！？]', text)
    
    # 过滤空句子
    return [s.strip() for s in sentences if s.strip()]

def vision_llm_chunk(binary: Any, vision_model: Any = None, prompt: str = "", callback=None) -> str:
    """
    视觉LLM处理函数的占位符实现
    用户需要根据自己的需求实现此函数
    """
    # 这是一个占位符实现
    # 实际使用时，用户需要根据自己的视觉模型来实现
    if callback:
        callback(50, "处理图像中...")
    
    # 返回示例文本
    return "这是一个占位符文本，请实现自己的视觉处理逻辑"

def vision_llm_describe_prompt(page: int = 1) -> str:
    """生成视觉描述提示词"""
    return f"请描述第{page}页的内容，包括文字、图像和表格等元素。"

def get_project_base_directory() -> str:
    """获取项目基础目录"""
    return str(Path(__file__).parent.parent)
'''
        
        utils_file = self.deepdoc_target / "utils.py"
        utils_file.write_text(utils_content, encoding='utf-8')
        
        print("  ✓ 创建工具文件")
    
    def _generate_requirements(self):
        """生成requirements.txt"""
        requirements = [
            "# DeepDoc核心依赖",
            "numpy>=1.26.0,<2.0.0",
            "opencv-python>=4.8.0",
            "pillow>=10.4.0",
            "",
            "# 机器学习推理",
            "onnxruntime>=1.15.0",
            "",
            "# 文档处理",
            "pdfplumber>=0.10.0",
            "pypdf>=5.0.0,<6.0.0",
            "openpyxl>=3.1.0,<4.0.0",
            "python-docx>=1.1.2",
            "python-pptx>=1.0.2",
            "pandas>=2.2.0,<3.0.0",
            "",
            "# 模型下载",
            "huggingface-hub>=0.19.0",
            "",
            "# 并发处理",
            "trio>=0.22.0",
            "",
            "# 机器学习",
            "scikit-learn>=1.5.0",
            "xgboost",
            "",
            "# 其他工具",
            "tqdm>=4.65.0",
            "requests>=2.32.0",
        ]
        
        req_file = self.output_path / "requirements.txt"
        req_file.write_text('\n'.join(requirements), encoding='utf-8')
        
        print("  ✓ 生成requirements.txt")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="从RAGFlow中分离DeepDoc模块")
    parser.add_argument(
        "--ragflow-path", 
        required=True,
        help="RAGFlow项目路径"
    )
    parser.add_argument(
        "--output-path",
        default="./standalone_deepdoc",
        help="输出路径（默认: ./standalone_deepdoc）"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制覆盖已存在的输出目录"
    )
    
    args = parser.parse_args()
    
    # 检查输出路径
    output_path = Path(args.output_path)
    if output_path.exists() and not args.force:
        print(f"错误: 输出路径已存在: {output_path}")
        print("使用 --force 参数强制覆盖")
        return
    
    try:
        # 创建迁移器并执行迁移
        migrator = DeepDocMigrator(args.ragflow_path, args.output_path)
        migrator.migrate()
        
        print("\n🎉 迁移成功完成！")
        print("\n下一步:")
        print(f"1. cd {args.output_path}")
        print("2. pip install -e .")
        print("3. 运行测试: python -m pytest tests/")
        print("4. 查看示例: python examples/integration_examples.py")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return 1


if __name__ == "__main__":
    main()
