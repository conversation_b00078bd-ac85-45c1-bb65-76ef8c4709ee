#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc Command Line Interface
提供命令行工具接口
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import List, Dict, Any

from . import __version__
from .core import DocumentProcessor, DocumentAnalyzer
from .vision import check_vision_availability
from .parser import check_parser_availability

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_common_args(parser: argparse.ArgumentParser):
    """设置通用参数"""
    parser.add_argument(
        '--version', 
        action='version', 
        version=f'DeepDoc {__version__}'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细输出'
    )
    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='./output',
        help='输出目录 (默认: ./output)'
    )


def ocr_cli():
    """OCR命令行接口"""
    parser = argparse.ArgumentParser(
        description='DeepDoc OCR - 光学字符识别工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  deepdoc-ocr --inputs image.jpg
  deepdoc-ocr --inputs ./images/ --output-dir ./ocr_results
  deepdoc-ocr --inputs document.pdf --threshold 0.7
        """
    )
    
    setup_common_args(parser)
    
    parser.add_argument(
        '--inputs', '-i',
        type=str,
        required=True,
        help='输入图像文件或目录路径'
    )
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.5,
        help='OCR置信度阈值 (默认: 0.5)'
    )
    parser.add_argument(
        '--format',
        choices=['json', 'txt'],
        default='json',
        help='输出格式 (默认: json)'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查视觉模块可用性
    if not check_vision_availability():
        logger.error("视觉模块不可用，请检查依赖安装")
        sys.exit(1)
    
    try:
        analyzer = DocumentAnalyzer()
        
        input_path = Path(args.inputs)
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if input_path.is_file():
            # 单个文件
            result = analyzer.analyze_image(
                input_path,
                include_layout=False,
                include_table=False,
                threshold=args.threshold
            )
            
            output_file = output_dir / f"{input_path.stem}_ocr.{args.format}"
            save_result(result, output_file, args.format)
            
        elif input_path.is_dir():
            # 目录
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))
            
            for image_file in image_files:
                result = analyzer.analyze_image(
                    image_file,
                    include_layout=False,
                    include_table=False,
                    threshold=args.threshold
                )
                
                output_file = output_dir / f"{image_file.stem}_ocr.{args.format}"
                save_result(result, output_file, args.format)
        
        logger.info(f"OCR处理完成，结果保存到: {output_dir}")
        
    except Exception as e:
        logger.error(f"OCR处理失败: {e}")
        sys.exit(1)


def layout_cli():
    """布局分析命令行接口"""
    parser = argparse.ArgumentParser(
        description='DeepDoc Layout - 文档布局分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  deepdoc-layout --inputs image.jpg
  deepdoc-layout --inputs ./images/ --threshold 0.7
        """
    )
    
    setup_common_args(parser)
    
    parser.add_argument(
        '--inputs', '-i',
        type=str,
        required=True,
        help='输入图像文件或目录路径'
    )
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.7,
        help='布局检测置信度阈值 (默认: 0.7)'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查视觉模块可用性
    if not check_vision_availability():
        logger.error("视觉模块不可用，请检查依赖安装")
        sys.exit(1)
    
    try:
        analyzer = DocumentAnalyzer()
        
        input_path = Path(args.inputs)
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if input_path.is_file():
            result = analyzer.analyze_image(
                input_path,
                include_ocr=False,
                include_table=False,
                threshold=args.threshold
            )
            
            output_file = output_dir / f"{input_path.stem}_layout.json"
            save_result(result, output_file, 'json')
            
        elif input_path.is_dir():
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))
            
            for image_file in image_files:
                result = analyzer.analyze_image(
                    image_file,
                    include_ocr=False,
                    include_table=False,
                    threshold=args.threshold
                )
                
                output_file = output_dir / f"{image_file.stem}_layout.json"
                save_result(result, output_file, 'json')
        
        logger.info(f"布局分析完成，结果保存到: {output_dir}")
        
    except Exception as e:
        logger.error(f"布局分析失败: {e}")
        sys.exit(1)


def table_cli():
    """表格分析命令行接口"""
    parser = argparse.ArgumentParser(
        description='DeepDoc Table - 表格结构识别工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  deepdoc-table --inputs table.jpg
  deepdoc-table --inputs ./tables/ --threshold 0.2
        """
    )
    
    setup_common_args(parser)
    
    parser.add_argument(
        '--inputs', '-i',
        type=str,
        required=True,
        help='输入图像文件或目录路径'
    )
    parser.add_argument(
        '--threshold',
        type=float,
        default=0.2,
        help='表格检测置信度阈值 (默认: 0.2)'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查视觉模块可用性
    if not check_vision_availability():
        logger.error("视觉模块不可用，请检查依赖安装")
        sys.exit(1)
    
    try:
        analyzer = DocumentAnalyzer()
        
        input_path = Path(args.inputs)
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if input_path.is_file():
            result = analyzer.analyze_image(
                input_path,
                include_ocr=False,
                include_layout=False,
                threshold=args.threshold
            )
            
            output_file = output_dir / f"{input_path.stem}_table.json"
            save_result(result, output_file, 'json')
            
        elif input_path.is_dir():
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))
            
            for image_file in image_files:
                result = analyzer.analyze_image(
                    image_file,
                    include_ocr=False,
                    include_layout=False,
                    threshold=args.threshold
                )
                
                output_file = output_dir / f"{image_file.stem}_table.json"
                save_result(result, output_file, 'json')
        
        logger.info(f"表格分析完成，结果保存到: {output_dir}")
        
    except Exception as e:
        logger.error(f"表格分析失败: {e}")
        sys.exit(1)


def parse_cli():
    """文档解析命令行接口"""
    parser = argparse.ArgumentParser(
        description='DeepDoc Parse - 文档解析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  deepdoc-parse --inputs document.pdf
  deepdoc-parse --inputs ./documents/ --type auto
  deepdoc-parse --inputs file.docx --type docx
        """
    )
    
    setup_common_args(parser)
    
    parser.add_argument(
        '--inputs', '-i',
        type=str,
        required=True,
        help='输入文档文件或目录路径'
    )
    parser.add_argument(
        '--type', '-t',
        choices=['auto', 'pdf', 'docx', 'excel', 'ppt', 'html', 'json', 'markdown', 'txt'],
        default='auto',
        help='文档类型 (默认: auto)'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查解析器模块可用性
    if not check_parser_availability():
        logger.error("解析器模块不可用，请检查依赖安装")
        sys.exit(1)
    
    try:
        processor = DocumentProcessor()
        
        input_path = Path(args.inputs)
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if input_path.is_file():
            result = processor.parse(input_path, args.type)
            
            output_file = output_dir / f"{input_path.stem}_parsed.json"
            save_result(result, output_file, 'json')
            
        elif input_path.is_dir():
            doc_files = []
            for ext in ['.pdf', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.html', '.htm', '.json', '.md', '.txt']:
                doc_files.extend(input_path.glob(f"*{ext}"))
                doc_files.extend(input_path.glob(f"*{ext.upper()}"))
            
            for doc_file in doc_files:
                result = processor.parse(doc_file, args.type)
                
                output_file = output_dir / f"{doc_file.stem}_parsed.json"
                save_result(result, output_file, 'json')
        
        logger.info(f"文档解析完成，结果保存到: {output_dir}")
        
    except Exception as e:
        logger.error(f"文档解析失败: {e}")
        sys.exit(1)


def save_result(result: Any, output_file: Path, format_type: str):
    """保存结果到文件"""
    if format_type == 'json':
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    elif format_type == 'txt':
        with open(output_file, 'w', encoding='utf-8') as f:
            if isinstance(result, dict) and 'ocr' in result:
                # OCR结果
                for item in result['ocr']:
                    f.write(f"{item.get('text', '')}\n")
            else:
                f.write(str(result))
    
    logger.info(f"结果已保存到: {output_file}")


if __name__ == '__main__':
    # 根据脚本名称选择对应的CLI
    script_name = Path(sys.argv[0]).name
    
    if 'ocr' in script_name:
        ocr_cli()
    elif 'layout' in script_name:
        layout_cli()
    elif 'table' in script_name:
        table_cli()
    elif 'parse' in script_name:
        parse_cli()
    else:
        print("请使用具体的命令行工具:")
        print("  deepdoc-ocr    - OCR识别")
        print("  deepdoc-layout - 布局分析")
        print("  deepdoc-table  - 表格分析")
        print("  deepdoc-parse  - 文档解析")
