#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone DeepDoc - Advanced Document Understanding Toolkit

This is a standalone version of DeepDoc extracted from RAGFlow,
designed to be used independently by other RAG systems.

Main Components:
- Vision: OCR, Layout Recognition, Table Structure Recognition
- Parser: Document parsers for PDF, DOCX, Excel, PPT, etc.
"""

__version__ = "1.0.0"
__author__ = "DeepDoc Community"
__email__ = "<EMAIL>"

# 导入核心组件
from .vision import OCR, LayoutRecognizer, TableStructureRecognizer
from .parser import (
    PdfParser, 
    DocxParser, 
    ExcelParser, 
    PptParser, 
    HtmlParser, 
    JsonParser, 
    MarkdownParser, 
    TxtParser
)
from .core import DocumentProcessor, DocumentAnalyzer

# 导出主要接口
__all__ = [
    # Version info
    "__version__",
    "__author__", 
    "__email__",
    
    # Vision components
    "OCR",
    "LayoutRecognizer", 
    "TableStructureRecognizer",
    
    # Parser components
    "PdfParser",
    "DocxParser",
    "ExcelParser", 
    "PptParser",
    "HtmlParser",
    "JsonParser",
    "MarkdownParser",
    "TxtParser",
    
    # High-level interfaces
    "DocumentProcessor",
    "DocumentAnalyzer",
]

# 便捷函数
def create_document_processor(config=None):
    """
    创建文档处理器实例
    
    Args:
        config (dict, optional): 配置参数
        
    Returns:
        DocumentProcessor: 文档处理器实例
    """
    return DocumentProcessor(config)

def create_document_analyzer(config=None):
    """
    创建文档分析器实例
    
    Args:
        config (dict, optional): 配置参数
        
    Returns:
        DocumentAnalyzer: 文档分析器实例
    """
    return DocumentAnalyzer(config)

# 快速使用示例
def quick_parse(file_path, parser_type="auto", **kwargs):
    """
    快速解析文档
    
    Args:
        file_path (str): 文档路径
        parser_type (str): 解析器类型，支持 "auto", "pdf", "docx", "excel", "ppt"
        **kwargs: 其他参数
        
    Returns:
        list: 解析结果
    """
    processor = create_document_processor()
    return processor.parse(file_path, parser_type, **kwargs)

def quick_ocr(image_path, **kwargs):
    """
    快速OCR识别
    
    Args:
        image_path (str): 图片路径
        **kwargs: 其他参数
        
    Returns:
        list: OCR结果
    """
    ocr = OCR()
    return ocr.recognize(image_path, **kwargs)

def quick_layout_analysis(image_path, **kwargs):
    """
    快速布局分析
    
    Args:
        image_path (str): 图片路径
        **kwargs: 其他参数
        
    Returns:
        list: 布局分析结果
    """
    layout_recognizer = LayoutRecognizer()
    return layout_recognizer.analyze(image_path, **kwargs)

# 配置管理
class Config:
    """全局配置管理"""
    
    def __init__(self):
        self.model_cache_dir = None
        self.device = "cpu"
        self.batch_size = 16
        self.ocr_threshold = 0.5
        self.layout_threshold = 0.7
        self.table_threshold = 0.2
        
    def set_model_cache_dir(self, cache_dir):
        """设置模型缓存目录"""
        self.model_cache_dir = cache_dir
        
    def set_device(self, device):
        """设置计算设备"""
        self.device = device
        
    def to_dict(self):
        """转换为字典"""
        return {
            "model_cache_dir": self.model_cache_dir,
            "device": self.device,
            "batch_size": self.batch_size,
            "ocr_threshold": self.ocr_threshold,
            "layout_threshold": self.layout_threshold,
            "table_threshold": self.table_threshold,
        }

# 全局配置实例
config = Config()

# 设置日志
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())
