#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc集成示例
展示如何将DeepDoc集成到不同的RAG系统中
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# 添加deepdoc到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

import deepdoc


# ==================== LangChain集成示例 ====================

def langchain_integration_example():
    """LangChain集成示例"""
    print("=== LangChain集成示例 ===")
    
    try:
        from langchain.docstore.document import Document
        from langchain.document_loaders.base import BaseLoader
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        from langchain.vectorstores import Chroma
        from langchain.embeddings import HuggingFaceEmbeddings
        
        class DeepDocLoader(BaseLoader):
            """基于DeepDoc的LangChain文档加载器"""
            
            def __init__(self, file_path: str, parser_type: str = "auto", **kwargs):
                self.file_path = file_path
                self.parser_type = parser_type
                self.kwargs = kwargs
                
            def load(self) -> List[Document]:
                """加载文档"""
                processor = deepdoc.create_document_processor()
                results = processor.parse(self.file_path, self.parser_type, **self.kwargs)
                
                documents = []
                for i, chunk in enumerate(results):
                    doc = Document(
                        page_content=chunk.get('text', ''),
                        metadata={
                            'source': self.file_path,
                            'chunk_id': i,
                            'page': chunk.get('page', 0),
                            'type': chunk.get('type', 'text'),
                            'bbox': chunk.get('bbox', None),
                        }
                    )
                    documents.append(doc)
                return documents
        
        # 使用示例
        if os.path.exists("sample.pdf"):
            loader = DeepDocLoader("sample.pdf")
            docs = loader.load()
            
            # 文本分割
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=200
            )
            splits = text_splitter.split_documents(docs)
            
            print(f"加载了 {len(docs)} 个文档块，分割为 {len(splits)} 个片段")
            
            # 创建向量存储（需要安装相应依赖）
            # embeddings = HuggingFaceEmbeddings()
            # vectorstore = Chroma.from_documents(splits, embeddings)
            
        else:
            print("请提供sample.pdf文件进行测试")
            
    except ImportError:
        print("LangChain未安装，跳过集成示例")


# ==================== LlamaIndex集成示例 ====================

def llamaindex_integration_example():
    """LlamaIndex集成示例"""
    print("\n=== LlamaIndex集成示例 ===")
    
    try:
        from llama_index.core import Document, VectorStoreIndex
        from llama_index.core.readers.base import BaseReader
        from llama_index.core.node_parser import SimpleNodeParser
        
        class DeepDocReader(BaseReader):
            """基于DeepDoc的LlamaIndex文档读取器"""
            
            def __init__(self, parser_type: str = "auto", **kwargs):
                self.parser_type = parser_type
                self.kwargs = kwargs
                
            def load_data(self, file_path: str) -> List[Document]:
                """加载数据"""
                processor = deepdoc.create_document_processor()
                results = processor.parse(file_path, self.parser_type, **self.kwargs)
                
                documents = []
                for i, chunk in enumerate(results):
                    doc = Document(
                        text=chunk.get('text', ''),
                        metadata={
                            'file_path': file_path,
                            'chunk_id': i,
                            'page': chunk.get('page', 0),
                            'type': chunk.get('type', 'text'),
                            'bbox': chunk.get('bbox', None),
                        }
                    )
                    documents.append(doc)
                return documents
        
        # 使用示例
        if os.path.exists("sample.pdf"):
            reader = DeepDocReader()
            docs = reader.load_data("sample.pdf")
            
            # 创建节点解析器
            node_parser = SimpleNodeParser.from_defaults()
            nodes = node_parser.get_nodes_from_documents(docs)
            
            print(f"加载了 {len(docs)} 个文档，解析为 {len(nodes)} 个节点")
            
            # 创建索引（需要设置LLM和嵌入模型）
            # index = VectorStoreIndex(nodes)
            
        else:
            print("请提供sample.pdf文件进行测试")
            
    except ImportError:
        print("LlamaIndex未安装，跳过集成示例")


# ==================== 自定义RAG系统集成示例 ====================

class CustomRAGSystem:
    """自定义RAG系统示例"""
    
    def __init__(self):
        self.document_processor = deepdoc.create_document_processor()
        self.document_analyzer = deepdoc.create_document_analyzer()
        self.documents = []
        
    def add_document(self, file_path: str, parser_type: str = "auto") -> Dict[str, Any]:
        """添加文档到系统"""
        print(f"正在处理文档: {file_path}")
        
        # 解析文档
        chunks = self.document_processor.parse(file_path, parser_type)
        
        # 如果是图像文件，进行视觉分析
        if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff')):
            vision_results = self.document_analyzer.analyze_image(file_path)
            chunks.extend(self._process_vision_results(vision_results))
        
        # 存储文档
        doc_info = {
            'file_path': file_path,
            'chunks': chunks,
            'metadata': {
                'total_chunks': len(chunks),
                'parser_type': parser_type,
            }
        }
        
        self.documents.append(doc_info)
        return doc_info
    
    def _process_vision_results(self, vision_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理视觉分析结果"""
        chunks = []
        
        # 处理OCR结果
        if 'ocr' in vision_results:
            for ocr_item in vision_results['ocr']:
                chunks.append({
                    'text': ocr_item.get('text', ''),
                    'type': 'ocr',
                    'bbox': ocr_item.get('bbox', None),
                    'confidence': ocr_item.get('confidence', 0),
                })
        
        # 处理布局结果
        if 'layout' in vision_results:
            for layout_item in vision_results['layout']:
                chunks.append({
                    'text': layout_item.get('text', ''),
                    'type': f"layout_{layout_item.get('type', 'unknown')}",
                    'bbox': layout_item.get('bbox', None),
                    'confidence': layout_item.get('confidence', 0),
                })
        
        # 处理表格结果
        if 'tables' in vision_results:
            for table_item in vision_results['tables']:
                chunks.append({
                    'text': table_item.get('text', ''),
                    'type': 'table',
                    'bbox': table_item.get('bbox', None),
                    'structure': table_item.get('structure', None),
                })
        
        return chunks
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """简单的文本搜索（实际应用中应使用向量搜索）"""
        results = []
        
        for doc in self.documents:
            for chunk in doc['chunks']:
                text = chunk.get('text', '').lower()
                if query.lower() in text:
                    results.append({
                        'document': doc['file_path'],
                        'chunk': chunk,
                        'relevance_score': text.count(query.lower()) / len(text.split()),
                    })
        
        # 按相关性排序
        results.sort(key=lambda x: x['relevance_score'], reverse=True)
        return results[:top_k]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        total_chunks = sum(len(doc['chunks']) for doc in self.documents)
        
        chunk_types = {}
        for doc in self.documents:
            for chunk in doc['chunks']:
                chunk_type = chunk.get('type', 'unknown')
                chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
        
        return {
            'total_documents': len(self.documents),
            'total_chunks': total_chunks,
            'chunk_types': chunk_types,
            'average_chunks_per_doc': total_chunks / len(self.documents) if self.documents else 0,
        }


def custom_rag_example():
    """自定义RAG系统示例"""
    print("\n=== 自定义RAG系统示例 ===")
    
    rag_system = CustomRAGSystem()
    
    # 添加测试文档
    test_files = ["sample.pdf", "sample.docx", "sample.xlsx"]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            doc_info = rag_system.add_document(file_path)
            print(f"添加文档成功: {doc_info['metadata']}")
    
    # 显示统计信息
    stats = rag_system.get_statistics()
    print(f"\n系统统计: {stats}")
    
    # 搜索示例
    if rag_system.documents:
        query = "测试"
        results = rag_system.search(query)
        print(f"\n搜索 '{query}' 的结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. 文档: {result['document']}")
            print(f"   文本: {result['chunk']['text'][:100]}...")
            print(f"   相关性: {result['relevance_score']:.3f}")


# ==================== 批处理示例 ====================

def batch_processing_example():
    """批处理示例"""
    print("\n=== 批处理示例 ===")
    
    # 创建测试目录和文件
    test_dir = Path("test_documents")
    if not test_dir.exists():
        print("请创建test_documents目录并放入测试文件")
        return
    
    # 获取所有文档文件
    doc_files = []
    for ext in ['.pdf', '.docx', '.xlsx', '.pptx', '.txt', '.md']:
        doc_files.extend(test_dir.glob(f"*{ext}"))
    
    if not doc_files:
        print("test_documents目录中没有找到支持的文档文件")
        return
    
    print(f"找到 {len(doc_files)} 个文档文件")
    
    # 批量处理
    processor = deepdoc.create_document_processor()
    results = processor.batch_parse([str(f) for f in doc_files])
    
    # 统计结果
    total_chunks = 0
    for file_path, chunks in results.items():
        chunk_count = len(chunks)
        total_chunks += chunk_count
        print(f"{Path(file_path).name}: {chunk_count} 个块")
    
    print(f"\n总计处理了 {total_chunks} 个文档块")


# ==================== 主函数 ====================

def main():
    """主函数"""
    print("DeepDoc集成示例")
    print("=" * 50)
    
    # 检查模块可用性
    print(f"Vision模块可用: {deepdoc.vision.check_vision_availability()}")
    print(f"Parser模块可用: {deepdoc.parser.check_parser_availability()}")
    print()
    
    # 运行示例
    langchain_integration_example()
    llamaindex_integration_example()
    custom_rag_example()
    batch_processing_example()
    
    print("\n示例运行完成！")


if __name__ == "__main__":
    main()
