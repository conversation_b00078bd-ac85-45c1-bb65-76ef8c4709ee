#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepDoc Vision Module
视觉理解模块，包含OCR、布局识别、表格结构识别等功能
"""

import sys
import threading
import logging
from typing import List, Dict, Any, Optional, Union
import numpy as np
from PIL import Image

logger = logging.getLogger(__name__)

# 全局锁，用于pdfplumber的线程安全
LOCK_KEY_pdfplumber = "global_shared_lock_pdfplumber"
if LOCK_KEY_pdfplumber not in sys.modules:
    sys.modules[LOCK_KEY_pdfplumber] = threading.Lock()

# 导入具体实现
try:
    from .ocr import OCREngine
    from .layout_recognizer import LayoutRecognizerEngine  
    from .table_structure_recognizer import TableStructureRecognizerEngine
    
    # 创建统一接口
    class OCR:
        """OCR识别器统一接口"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            self.config = config or {}
            self.engine = OCREngine(self.config)
            
        def recognize(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> List[Dict[str, Any]]:
            """
            识别图像中的文字
            
            Args:
                image: 图像路径、numpy数组或PIL图像
                **kwargs: 其他参数
                
            Returns:
                list: OCR结果列表
            """
            return self.engine.recognize(image, **kwargs)
            
        def detect(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> List[Dict[str, Any]]:
            """
            检测图像中的文字区域
            
            Args:
                image: 图像路径、numpy数组或PIL图像
                **kwargs: 其他参数
                
            Returns:
                list: 文字区域检测结果
            """
            return self.engine.detect(image, **kwargs)
    
    class LayoutRecognizer:
        """布局识别器统一接口"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            self.config = config or {}
            self.engine = LayoutRecognizerEngine(self.config)
            
        def analyze(self, images: List[Union[str, np.ndarray, Image.Image]], **kwargs) -> List[Dict[str, Any]]:
            """
            分析图像布局
            
            Args:
                images: 图像列表
                **kwargs: 其他参数
                
            Returns:
                list: 布局分析结果
            """
            return self.engine.analyze(images, **kwargs)
            
        def recognize_layout(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> Dict[str, Any]:
            """
            识别单个图像的布局
            
            Args:
                image: 图像
                **kwargs: 其他参数
                
            Returns:
                dict: 布局识别结果
            """
            results = self.analyze([image], **kwargs)
            return results[0] if results else {}
    
    class TableStructureRecognizer:
        """表格结构识别器统一接口"""
        
        def __init__(self, config: Optional[Dict[str, Any]] = None):
            self.config = config or {}
            self.engine = TableStructureRecognizerEngine(self.config)
            
        def analyze(self, images: List[Union[str, np.ndarray, Image.Image]], **kwargs) -> List[Dict[str, Any]]:
            """
            分析表格结构
            
            Args:
                images: 图像列表
                **kwargs: 其他参数
                
            Returns:
                list: 表格结构分析结果
            """
            return self.engine.analyze(images, **kwargs)
            
        def recognize_table(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> Dict[str, Any]:
            """
            识别单个图像中的表格结构
            
            Args:
                image: 图像
                **kwargs: 其他参数
                
            Returns:
                dict: 表格结构识别结果
            """
            results = self.analyze([image], **kwargs)
            return results[0] if results else {}
    
    # 标记模块可用
    VISION_AVAILABLE = True
    logger.info("DeepDoc Vision模块加载成功")
    
except ImportError as e:
    logger.warning(f"DeepDoc Vision模块加载失败: {e}")
    
    # 创建占位符类
    class OCR:
        def __init__(self, config=None):
            raise ImportError("OCR模块不可用，请检查依赖安装")
            
    class LayoutRecognizer:
        def __init__(self, config=None):
            raise ImportError("布局识别模块不可用，请检查依赖安装")
            
    class TableStructureRecognizer:
        def __init__(self, config=None):
            raise ImportError("表格结构识别模块不可用，请检查依赖安装")
    
    VISION_AVAILABLE = False

# 导出接口
__all__ = [
    "OCR",
    "LayoutRecognizer", 
    "TableStructureRecognizer",
    "VISION_AVAILABLE",
]

# 便捷函数
def create_ocr(config: Optional[Dict[str, Any]] = None) -> OCR:
    """创建OCR实例"""
    return OCR(config)

def create_layout_recognizer(config: Optional[Dict[str, Any]] = None) -> LayoutRecognizer:
    """创建布局识别器实例"""
    return LayoutRecognizer(config)

def create_table_recognizer(config: Optional[Dict[str, Any]] = None) -> TableStructureRecognizer:
    """创建表格结构识别器实例"""
    return TableStructureRecognizer(config)

def quick_ocr(image: Union[str, np.ndarray, Image.Image], **kwargs) -> List[Dict[str, Any]]:
    """快速OCR识别"""
    ocr = create_ocr()
    return ocr.recognize(image, **kwargs)

def quick_layout_analysis(image: Union[str, np.ndarray, Image.Image], **kwargs) -> Dict[str, Any]:
    """快速布局分析"""
    layout_recognizer = create_layout_recognizer()
    return layout_recognizer.recognize_layout(image, **kwargs)

def quick_table_analysis(image: Union[str, np.ndarray, Image.Image], **kwargs) -> Dict[str, Any]:
    """快速表格分析"""
    table_recognizer = create_table_recognizer()
    return table_recognizer.recognize_table(image, **kwargs)

# 检查模块可用性
def check_vision_availability() -> bool:
    """检查视觉模块是否可用"""
    return VISION_AVAILABLE

def get_vision_info() -> Dict[str, Any]:
    """获取视觉模块信息"""
    return {
        "available": VISION_AVAILABLE,
        "components": {
            "ocr": VISION_AVAILABLE,
            "layout_recognizer": VISION_AVAILABLE,
            "table_recognizer": VISION_AVAILABLE,
        }
    }
